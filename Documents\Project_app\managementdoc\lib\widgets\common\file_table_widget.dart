import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../core/constants/app_colors.dart';
import '../../models/document_model.dart';

enum FileTableMode {
  view, // View only (like home screen)
  select, // Selection mode (like add files to category)
  manage, // Management mode (like category files)
}

enum TableColumnType {
  checkbox,
  fileName,
  fileType,
  fileSize,
  uploadDate,
  owner,
  status,
  actions,
  custom,
}

class TableColumn {
  final TableColumnType type;
  final String title;
  final TableColumnWidth width;
  final TextAlign alignment;
  final bool isCheckbox;
  final String Function(DocumentModel)? customValue;

  const TableColumn({
    required this.type,
    required this.title,
    required this.width,
    this.alignment = TextAlign.left,
    this.isCheckbox = false,
    this.customValue,
  });
}

class FileTableWidget extends StatefulWidget {
  final List<DocumentModel> documents;
  final FileTableMode mode;
  final String title;
  final bool isLoading;
  final bool showFilter;
  final bool showRefresh;
  final bool showSelectAll;
  final Set<String>? selectedFiles;
  final VoidCallback? onRefresh;
  final VoidCallback? onFilter;
  final Function(DocumentModel)? onDocumentTap;
  final Function(DocumentModel)? onDocumentMenu;
  final Function(String, bool)? onFileSelect;
  final Function(bool?)? onSelectAll;
  final Widget? emptyStateWidget;
  final List<TableColumn>? customColumns;

  const FileTableWidget({
    super.key,
    required this.documents,
    this.mode = FileTableMode.view,
    this.title = 'Files',
    this.isLoading = false,
    this.showFilter = true,
    this.showRefresh = false,
    this.showSelectAll = false,
    this.selectedFiles,
    this.onRefresh,
    this.onFilter,
    this.onDocumentTap,
    this.onDocumentMenu,
    this.onFileSelect,
    this.onSelectAll,
    this.emptyStateWidget,
    this.customColumns,
  });

  @override
  State<FileTableWidget> createState() => _FileTableWidgetState();
}

class _FileTableWidgetState extends State<FileTableWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and actions
          if (widget.title.isNotEmpty) ...[
            _buildHeader(),
            const SizedBox(height: 16),
          ],
          // Table container
          Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadow,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Table Header
                _buildTableHeader(),
                // Table Content
                widget.isLoading
                    ? Container(
                        height: 200,
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: AppColors.primary,
                          ),
                        ),
                      )
                    : widget.documents.isEmpty
                    ? _buildEmptyState()
                    : _buildTableContent(),
              ],
            ),
          ),
          const SizedBox(height: 100), // Extra space for bottom navigation
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.title,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        Row(
          children: [
            if (widget.showRefresh && widget.onRefresh != null)
              IconButton(
                onPressed: widget.onRefresh,
                icon: const Icon(
                  Icons.refresh,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                tooltip: 'Refresh',
              ),
            if (widget.showFilter && widget.onFilter != null)
              IconButton(
                onPressed: widget.onFilter,
                icon: const Icon(
                  Icons.filter_list,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                tooltip: 'Filter Files',
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildTableHeader() {
    final columns = widget.customColumns ?? _getDefaultColumns();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: AppColors.border, width: 1)),
      ),
      child: Table(
        columnWidths: _getColumnWidths(columns),
        children: [
          TableRow(
            children: columns.map((column) {
              if (column.isCheckbox && widget.mode == FileTableMode.select) {
                return TableCell(
                  child: widget.showSelectAll
                      ? Checkbox(
                          value:
                              widget.selectedFiles?.length ==
                                  widget.documents.length &&
                              widget.documents.isNotEmpty,
                          onChanged: widget.onSelectAll,
                          activeColor: AppColors.primary,
                        )
                      : const SizedBox.shrink(),
                );
              }
              return TableCell(
                child: Text(
                  column.title,
                  style: _getTableHeaderStyle(),
                  textAlign: column.alignment,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTableContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: widget.documents
          .map((document) => _buildDocumentRow(document))
          .toList(),
    );
  }

  Widget _buildDocumentRow(DocumentModel document) {
    final columns = widget.customColumns ?? _getDefaultColumns();
    final isSelected = widget.selectedFiles?.contains(document.id) ?? false;

    return Container(
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.primary.withValues(alpha: 0.05)
            : Colors.transparent,
        border: const Border(
          bottom: BorderSide(color: AppColors.border, width: 0.5),
        ),
      ),
      child: InkWell(
        onTap: widget.onDocumentTap != null
            ? () => widget.onDocumentTap!(document)
            : null,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Table(
            columnWidths: _getColumnWidths(columns),
            children: [
              TableRow(
                children: columns.map((column) {
                  return TableCell(
                    verticalAlignment: TableCellVerticalAlignment.middle,
                    child: _buildCellContent(column, document),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCellContent(TableColumn column, DocumentModel document) {
    switch (column.type) {
      case TableColumnType.checkbox:
        if (widget.mode == FileTableMode.select) {
          final isSelected =
              widget.selectedFiles?.contains(document.id) ?? false;
          return Checkbox(
            value: isSelected,
            onChanged: widget.onFileSelect != null
                ? (value) => widget.onFileSelect!(document.id, value ?? false)
                : null,
            activeColor: AppColors.primary,
          );
        }
        return const SizedBox.shrink();

      case TableColumnType.fileName:
        return Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: _getFileTypeColor(
                  document.fileType,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                _getFileTypeIcon(document.fileType),
                color: _getFileTypeColor(document.fileType),
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                document.fileName,
                style: _getTableTextStyle(),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );

      case TableColumnType.fileType:
        return Text(
          _getFileTypeLabel(document.fileType),
          style: _getTableTextStyle(),
          textAlign: column.alignment,
        );

      case TableColumnType.fileSize:
        return Text(
          _formatFileSize(document.fileSize),
          style: _getTableTextStyle(),
          textAlign: column.alignment,
        );

      case TableColumnType.uploadDate:
        return Text(
          _formatDate(document.uploadedAt),
          style: _getTableTextStyle(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: column.alignment,
        );

      case TableColumnType.owner:
        return Text(
          document.uploadedBy,
          style: _getTableTextStyle(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: column.alignment,
        );

      case TableColumnType.status:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(document.status).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            document.status.toUpperCase(),
            style: GoogleFonts.poppins(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(document.status),
            ),
          ),
        );

      case TableColumnType.actions:
        return Center(
          child: IconButton(
            onPressed: widget.onDocumentMenu != null
                ? () => widget.onDocumentMenu!(document)
                : null,
            icon: const Icon(
              Icons.more_vert,
              color: AppColors.textSecondary,
              size: 20,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
        );

      case TableColumnType.custom:
        return Text(
          column.customValue?.call(document) ?? '',
          style: _getTableTextStyle(),
          textAlign: column.alignment,
        );

      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildEmptyState() {
    if (widget.emptyStateWidget != null) {
      return widget.emptyStateWidget!;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No files found',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Files will appear here when available',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppColors.textSecondary.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  List<TableColumn> _getDefaultColumns() {
    switch (widget.mode) {
      case FileTableMode.select:
        return [
          TableColumn(
            type: TableColumnType.checkbox,
            title: '',
            width: const FixedColumnWidth(1000),
            isCheckbox: true,
          ),
          TableColumn(
            type: TableColumnType.fileName,
            title: 'File Name',
            width: const FlexColumnWidth(3),
          ),
          TableColumn(
            type: TableColumnType.fileType,
            title: 'Type',
            width: const FlexColumnWidth(2),
            alignment: TextAlign.center,
          ),
          TableColumn(
            type: TableColumnType.uploadDate,
            title: 'Date',
            width: const FlexColumnWidth(2),
            alignment: TextAlign.center,
          ),
        ];

      case FileTableMode.manage:
        return [
          TableColumn(
            type: TableColumnType.fileName,
            title: 'Name',
            width: const FlexColumnWidth(4),
          ),
          TableColumn(
            type: TableColumnType.fileType,
            title: 'Type',
            width: const FlexColumnWidth(2),
            alignment: TextAlign.center,
          ),
          TableColumn(
            type: TableColumnType.uploadDate,
            title: 'Date',
            width: const FlexColumnWidth(2),
            alignment: TextAlign.center,
          ),
          TableColumn(
            type: TableColumnType.actions,
            title: 'Action',
            width: const FixedColumnWidth(50),
            alignment: TextAlign.center,
          ),
        ];

      case FileTableMode.view:
      default:
        return [
          TableColumn(
            type: TableColumnType.fileName,
            title: 'Name',
            width: const FlexColumnWidth(5),
          ),
          TableColumn(
            type: TableColumnType.uploadDate,
            title: 'Date',
            width: const FlexColumnWidth(1),
          ),
          TableColumn(
            type: TableColumnType.actions,
            title: 'Action',
            width: const FixedColumnWidth(50),
            alignment: TextAlign.center,
          ),
        ];
    }
  }

  Map<int, TableColumnWidth> _getColumnWidths(List<TableColumn> columns) {
    final Map<int, TableColumnWidth> widths = {};
    for (int i = 0; i < columns.length; i++) {
      widths[i] = columns[i].width;
    }
    return widths;
  }

  TextStyle _getTableHeaderStyle() {
    return GoogleFonts.poppins(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: AppColors.textPrimary,
    );
  }

  TextStyle _getTableTextStyle() {
    return GoogleFonts.poppins(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: AppColors.textPrimary,
    );
  }

  IconData _getFileTypeIcon(String fileType) {
    if (fileType.contains('pdf')) return Icons.picture_as_pdf;
    if (fileType.contains('word') || fileType.contains('doc'))
      return Icons.description;
    if (fileType.contains('excel') || fileType.contains('sheet'))
      return Icons.table_chart;
    if (fileType.contains('powerpoint') || fileType.contains('presentation'))
      return Icons.slideshow;
    if (fileType.contains('image')) return Icons.image;
    if (fileType.contains('video')) return Icons.video_file;
    if (fileType.contains('audio')) return Icons.audio_file;
    return Icons.insert_drive_file;
  }

  Color _getFileTypeColor(String fileType) {
    if (fileType.contains('pdf')) return Colors.red;
    if (fileType.contains('word') || fileType.contains('doc'))
      return Colors.blue;
    if (fileType.contains('excel') || fileType.contains('sheet'))
      return Colors.green;
    if (fileType.contains('powerpoint') || fileType.contains('presentation'))
      return Colors.orange;
    if (fileType.contains('image')) return Colors.purple;
    if (fileType.contains('video')) return Colors.pink;
    if (fileType.contains('audio')) return Colors.teal;
    return AppColors.textSecondary;
  }

  String _getFileTypeLabel(String fileType) {
    if (fileType.contains('pdf')) return 'PDF';
    if (fileType.contains('word') || fileType.contains('doc')) return 'Word';
    if (fileType.contains('excel') || fileType.contains('sheet'))
      return 'Excel';
    if (fileType.contains('powerpoint') || fileType.contains('presentation'))
      return 'PowerPoint';
    if (fileType.contains('image')) return 'Image';
    if (fileType.contains('video')) return 'Video';
    if (fileType.contains('audio')) return 'Audio';
    return 'File';
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'published':
        return AppColors.success;
      case 'pending':
      case 'processing':
        return AppColors.warning;
      case 'inactive':
      case 'draft':
        return AppColors.textSecondary;
      case 'error':
      case 'failed':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('MMM dd, yyyy').format(date);
    }
  }
}
